# 层级容器管理系统改进

## 概述

将原有的基于 `sortingOrder` 的层级管理系统改进为基于容器的层级管理系统，通过不同的容器来管理不同层级的拼块和 thickness，而不是通过设置 `sortingOrder` 来改变渲染顺序。

## 主要改变

### 1. SmartLayerManager 的改进

#### 新增功能
- 集成了 `LayerContainerManager` 来管理不同层级的容器
- 使用容器移动而不是 `sortingOrder` 来管理层级

#### 修改的方法
- `UpdatePieceLayer()`: 现在使用容器管理而不是 sortingOrder
- `ForceDraggingLayerUpdate()`: 改为移动对象到拖拽容器
- `ForceSetDraggingSortingOrder()` → `EnsureDraggingContainerPlacement()`: 重命名并改为确保对象在正确容器中

#### 删除的方法
- `CalculateSortingOrders()`: 不再需要计算 sortingOrder

#### 新增方法
- `Dispose()`: 清理容器管理器资源

### 2. LayerContainerManager 的使用

#### 容器类型
根据 `LayerConstants` 创建以下容器：
- `GridContainer`: 网格层 (sortingOrder: 0)
- `ThicknessContainer`: 普通 thickness 层 (sortingOrder: 1000)
- `PieceContainer`: 普通 piece 层 (sortingOrder: 2000)
- `OverlayThicknessContainer`: 叠加 thickness 层 (sortingOrder: 3000)
- `OverlayPieceContainer`: 叠加 piece 层 (sortingOrder: 4000)
- `DraggingThicknessContainer`: 拖拽 thickness 层 (sortingOrder: 5000)
- `DraggingPieceContainer`: 拖拽 piece 层 (sortingOrder: 6000)

#### 核心方法
- `GetPieceContainer(isDragging, isOverlay)`: 根据状态获取 piece 容器
- `GetThicknessContainer(isDragging, isOverlay)`: 根据状态获取 thickness 容器
- `MoveToContainer(obj, targetContainer, stackIndex)`: 移动对象到指定容器

### 3. 层级管理逻辑

#### 原理
- 每个容器都有固定的 `sortingOrder`，确保正确的渲染顺序
- 通过将对象移动到不同容器来改变其渲染层级
- 容器内部的对象顺序通过 `stackIndex` 管理

#### 状态映射
- 普通状态: `PieceContainer` / `ThicknessContainer`
- 叠加状态: `OverlayPieceContainer` / `OverlayThicknessContainer`
- 拖拽状态: `DraggingPieceContainer` / `DraggingThicknessContainer`

## 优势

### 1. 性能优化
- 减少频繁的 `sortingOrder` 设置操作
- 避免 FairyGUI 内部的重排序冲突
- 更好的批处理性能

### 2. 代码维护性
- 层级逻辑更清晰，通过容器结构一目了然
- 减少复杂的 sortingOrder 计算逻辑
- 更容易扩展新的层级类型

### 3. 稳定性
- 避免 sortingOrder 冲突和覆盖问题
- 更可预测的渲染顺序
- 减少层级管理相关的 bug

## 兼容性

### 保持兼容的接口
- `SmartLayerManager` 的公共接口基本保持不变
- `OperationLayer` 的调用方式无需改变
- 现有的拖拽和层级管理逻辑继续工作

### 内部实现变化
- 层级管理的内部实现完全重写
- 从 sortingOrder 管理改为容器管理
- 保持相同的外部行为

## 测试

创建了 `LayerContainerTest` 类来验证：
- 容器创建是否正确
- 容器获取逻辑是否正常
- 对象移动功能是否工作

## 使用方式

系统使用方式保持不变：
```csharp
// 注册拼块（自动创建 thickness 并放入正确容器）
layerManager.RegisterPiece(piece, gridPos);

// 开始拖拽（自动移动到拖拽容器）
layerManager.StartDragging(piece);

// 停止拖拽（自动移回普通容器）
layerManager.StopDragging(piece);

// 更新层级（自动根据状态选择容器）
layerManager.UpdatePieceLayer(piece);
```

## 注意事项

1. 容器的 `sortingOrder` 在创建时设置，之后不再修改
2. 对象的层级通过容器归属来管理，而不是个别的 `sortingOrder`
3. 容器内部的顺序通过 `stackIndex` 参数控制
4. 系统会自动处理坐标转换，确保对象位置正确
