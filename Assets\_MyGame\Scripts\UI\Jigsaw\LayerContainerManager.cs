using System.Collections.Generic;
using FairyGUI;
using UnityEngine;

/// <summary>
/// 层级容器管理器，为不同类型的layer创建专门的容器
/// </summary>
public class LayerContainerManager
{
    private GComponent rootContainer;
    private Dictionary<string, GComponent> layerContainers = new Dictionary<string, GComponent>();
    
    // 容器名称常量
    public const string GRID_CONTAINER = "GridContainer";
    public const string THICKNESS_CONTAINER = "ThicknessContainer";
    public const string PIECE_CONTAINER = "PieceContainer";
    public const string OVERLAY_THICKNESS_CONTAINER = "OverlayThicknessContainer";
    public const string OVERLAY_PIECE_CONTAINER = "OverlayPieceContainer";
    public const string DRAGGING_THICKNESS_CONTAINER = "DraggingThicknessContainer";
    public const string DRAGGING_PIECE_CONTAINER = "DraggingPieceContainer";
    
    public LayerContainerManager(GComponent root)
    {
        rootContainer = root;
        CreateLayerContainers();
    }
    
    /// <summary>
    /// 创建所有层级容器
    /// </summary>
    private void CreateLayerContainers()
    {
        // 按照LayerConstants的顺序创建容器，确保正确的渲染顺序
        CreateContainer(GRID_CONTAINER, LayerConstants.GRID_LAYER);
        CreateContainer(THICKNESS_CONTAINER, LayerConstants.THICKNESS_BASE);
        CreateContainer(PIECE_CONTAINER, LayerConstants.PIECE_BASE);
        CreateContainer(OVERLAY_THICKNESS_CONTAINER, LayerConstants.OVERLAY_THICKNESS_BASE);
        CreateContainer(OVERLAY_PIECE_CONTAINER, LayerConstants.OVERLAY_PIECE_BASE);
        CreateContainer(DRAGGING_THICKNESS_CONTAINER, LayerConstants.DRAGGING_THICKNESS_BASE);
        CreateContainer(DRAGGING_PIECE_CONTAINER, LayerConstants.DRAGGING_PIECE_BASE);
    }
    
    /// <summary>
    /// 创建单个容器
    /// </summary>
    /// <param name="containerName">容器名称</param>
    /// <param name="sortingOrder">容器的sortingOrder</param>
    private void CreateContainer(string containerName, int sortingOrder)
    {
        var container = new GComponent();
        container.name = containerName;
        container.SetSize(rootContainer.width, rootContainer.height);
        container.fairyBatching = false; // 禁用批处理，保持层级控制
        container.sortingOrder = sortingOrder;
        
        rootContainer.AddChild(container);
        layerContainers[containerName] = container;
        
        Debug.Log($"Created layer container: {containerName} with sortingOrder: {sortingOrder}");
    }
    
    /// <summary>
    /// 获取指定的层级容器
    /// </summary>
    /// <param name="containerName">容器名称</param>
    /// <returns>容器组件</returns>
    public GComponent GetContainer(string containerName)
    {
        if (layerContainers.TryGetValue(containerName, out GComponent container))
        {
            return container;
        }
        
        Debug.LogWarning($"Layer container not found: {containerName}");
        return null;
    }
    
    /// <summary>
    /// 根据层级信息获取thickness应该放置的容器
    /// </summary>
    /// <param name="isDragging">是否在拖拽状态</param>
    /// <param name="isOverlay">是否为叠加层</param>
    /// <returns>thickness容器</returns>
    public GComponent GetThicknessContainer(bool isDragging, bool isOverlay)
    {
        if (isDragging)
        {
            return GetContainer(DRAGGING_THICKNESS_CONTAINER);
        }
        else if (isOverlay)
        {
            return GetContainer(OVERLAY_THICKNESS_CONTAINER);
        }
        else
        {
            return GetContainer(THICKNESS_CONTAINER);
        }
    }
    
    /// <summary>
    /// 根据层级信息获取piece应该放置的容器
    /// </summary>
    /// <param name="isDragging">是否在拖拽状态</param>
    /// <param name="isOverlay">是否为叠加层</param>
    /// <returns>piece容器</returns>
    public GComponent GetPieceContainer(bool isDragging, bool isOverlay)
    {
        if (isDragging)
        {
            return GetContainer(DRAGGING_PIECE_CONTAINER);
        }
        else if (isOverlay)
        {
            return GetContainer(OVERLAY_PIECE_CONTAINER);
        }
        else
        {
            return GetContainer(PIECE_CONTAINER);
        }
    }
    
    /// <summary>
    /// 将对象移动到指定容器
    /// </summary>
    /// <param name="obj">要移动的对象</param>
    /// <param name="targetContainer">目标容器</param>
    /// <param name="stackIndex">在容器中的堆叠索引</param>
    public void MoveToContainer(GObject obj, GComponent targetContainer, int stackIndex = -1)
    {
        if (obj == null || targetContainer == null || obj.isDisposed) return;
        
        try
        {
            // 记录当前的位置信息
            Vector2 globalPos = Vector2.zero;
            if (obj.parent != null)
            {
                globalPos = obj.LocalToGlobal(Vector2.zero);
            }
            
            // 移除原有的父子关系
            if (obj.parent != null)
            {
                obj.parent.RemoveChild(obj, false);
            }
            
            // 添加到新容器
            if (stackIndex >= 0 && stackIndex < targetContainer.numChildren)
            {
                targetContainer.AddChildAt(obj, stackIndex);
            }
            else
            {
                targetContainer.AddChild(obj);
            }
            
            // 恢复位置
            if (obj.parent != null)
            {
                Vector2 localPos = targetContainer.GlobalToLocal(globalPos);
                obj.SetXY(localPos.x, localPos.y);
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Failed to move object to container: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 清理所有容器
    /// </summary>
    public void Clear()
    {
        foreach (var container in layerContainers.Values)
        {
            if (container != null && !container.isDisposed)
            {
                container.RemoveChildren();
            }
        }
    }
    
    /// <summary>
    /// 销毁所有容器
    /// </summary>
    public void Dispose()
    {
        foreach (var container in layerContainers.Values)
        {
            if (container != null && !container.isDisposed && container.parent != null)
            {
                container.parent.RemoveChild(container, true);
            }
        }
        layerContainers.Clear();
    }
}
