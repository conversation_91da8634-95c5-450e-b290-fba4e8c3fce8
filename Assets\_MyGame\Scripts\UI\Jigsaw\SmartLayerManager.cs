using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using FairyGUI;

/// <summary>
/// 智能层级管理器，负责管理所有拼块和thickness的层级
/// </summary>
public class SmartLayerManager
{
    private GComponent rootContainer;
    private GridPositionManager gridManager;
    private JigsawPanel parentPanel;
    private LayerContainerManager containerManager;
    private Dictionary<JigsawPiece, LayerInfo> pieceLayerInfo =
        new Dictionary<JigsawPiece, LayerInfo>();
    
    
    /// <summary>
    /// 层级信息结构
    /// </summary>
    public struct LayerInfo
    {
        public Vector2Int gridPosition;
        public int stackIndex;      // 在该格子中的堆叠索引
        public bool isDragging;
        public bool isOverlay;
        
        public LayerInfo(Vector2Int pos, int stack, bool drag, bool overlay)
        {
            gridPosition = pos;
            stackIndex = stack;
            isDragging = drag;
            isOverlay = overlay;
        }
    }
    
    public SmartLayerManager(GComponent container, JigsawPanel panel)
    {
        rootContainer = container;
        parentPanel = panel;
        gridManager = new GridPositionManager(this);
        containerManager = new LayerContainerManager(container);
    }
    
    /// <summary>
    /// 注册拼块到层级管理系统
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="gridPos">网格位置</param>
    public void RegisterPiece(JigsawPiece piece, Vector2Int gridPos)
    {
        if (piece == null) return;
        
        // 如果已经注册过，先注销
        if (pieceLayerInfo.ContainsKey(piece))
        {
            UnregisterPiece(piece);
        }
        
        // 确保拼块有thickness
        EnsurePieceHasThickness(piece);
        
        gridManager.AddPieceToGrid(gridPos, piece);
        
        var info = new LayerInfo(
            gridPos, 
            gridManager.GetStackIndex(gridPos, piece),
            false, 
            gridManager.IsOverlayPosition(gridPos, piece)
        );
        
        pieceLayerInfo[piece] = info;
        UpdatePieceLayer(piece);
    }
    
    /// <summary>
    /// 确保拼块有thickness
    /// </summary>
    /// <param name="piece">拼块</param>
    private void EnsurePieceHasThickness(JigsawPiece piece)
    {
        if (piece == null) return;
        
        // 检查是否已经有thickness
        var existingThickness = GetThicknessFor(piece);
        if (existingThickness != null) return;
        
        // 创建新的thickness
        try
        {
            if (rootContainer == null)
            {
                UnityEngine.Debug.LogError("SmartLayerManager: rootContainer is null!");
                return;
            }
            
            var thicknessClone = FairyGUI.UIPackage.CreateObject("Jigsaw", "JigsawThickness").asCom;
            var thickness = thicknessClone.GetChild("thickness").asLoader;
            
            var url = $"ui://Z_Image_{piece.imageIndex}/piece_{piece.pieceIndex}";
            thickness.url = url;
            
            // 设置thickness为半透明阴影效果
            thicknessClone.alpha = 0.6f;
            
            // 将thickness添加到对应的容器（默认为普通thickness容器）
            var thicknessContainer = containerManager.GetThicknessContainer(false, false);
            thicknessContainer.AddChild(thicknessClone);
            
            // 隐藏拼块自身的thickness
            piece.SetThicknessVisible(false);
            
            // 建立映射关系
            StorePieceThicknessMapping(piece, thicknessClone);
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"Failed to create thickness for piece {piece.pieceIndex}: {ex.Message}");
        }
    }
    
    // 映射存储
    private Dictionary<JigsawPiece, GComponent> pieceThicknessMap = new Dictionary<JigsawPiece, GComponent>();
    
    /// <summary>
    /// 存储拼块thickness映射
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="thickness">thickness组件</param>
    private void StorePieceThicknessMapping(JigsawPiece piece, GComponent thickness)
    {
        pieceThicknessMap[piece] = thickness;
    }
    
    /// <summary>
    /// 从层级管理系统移除拼块
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UnregisterPiece(JigsawPiece piece)
    {
        if (pieceLayerInfo.ContainsKey(piece))
        {
            var info = pieceLayerInfo[piece];
            gridManager.RemovePieceFromGrid(info.gridPosition, piece);
            pieceLayerInfo.Remove(piece);
            
            // 移除并销毁thickness
            RemovePieceThickness(piece);
        }
    }
    
    /// <summary>
    /// 移除拼块的thickness
    /// </summary>
    /// <param name="piece">拼块</param>
    private void RemovePieceThickness(JigsawPiece piece)
    {
        if (piece == null) return;
        
        if (pieceThicknessMap.TryGetValue(piece, out GComponent thickness))
        {
            // 从当前容器中移除,todo 复用thickness
            if (thickness.parent != null)
            {
                thickness.parent.RemoveChild(thickness, true);
            }

            // 移除映射关系
            pieceThicknessMap.Remove(piece);

            // 显示拼块自身的thickness
            piece.SetThicknessVisible(true);
        }
    }
    
    /// <summary>
    /// 更新拼块的层级
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UpdatePieceLayer(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || !pieceLayerInfo.ContainsKey(piece)) return;
        
        var info = pieceLayerInfo[piece];
        var thickness = GetThicknessFor(piece);
        
        // 使用容器管理层级，而不是sortingOrder
        try
        {
            // 获取piece应该放置的容器
            var targetPieceContainer = containerManager.GetPieceContainer(info.isDragging, info.isOverlay);
            var targetThicknessContainer = containerManager.GetThicknessContainer(info.isDragging, info.isOverlay);

            // 移动piece到正确的容器
            if (piece.parent != targetPieceContainer && !piece.isDisposed)
            {
                containerManager.MoveToContainer(piece, targetPieceContainer, info.stackIndex);
            }

            // 移动thickness到正确的容器
            if (thickness != null && thickness.parent != targetThicknessContainer && !thickness.isDisposed)
            {
                containerManager.MoveToContainer(thickness, targetThicknessContainer, info.stackIndex);
            }

            // 更新thickness的位置坐标
            if (thickness != null && !thickness.isDisposed)
            {
                UpdateThicknessPosition(piece, thickness);
            }
        }
        catch (System.Exception ex)
        {
            // 如果设置层级失败，记录错误但继续执行
            UnityEngine.Debug.LogError($"Failed to update piece layer: {ex.Message}");
        }
    }


    
    /// <summary>
    /// 更新thickness的位置坐标
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="thickness">thickness组件</param>
    private void UpdateThicknessPosition(JigsawPiece piece, GComponent thickness)
    {
        if (piece == null || thickness == null || piece.isDisposed) return;
        
        try
        {
            // 获取拼块的全局位置
            Vector2 pieceGlobalPos = piece.LocalToGlobal(Vector2.zero);
            
            // 转换为thickness父容器的本地坐标
            if (thickness.parent != null)
            {
                Vector2 thicknessLocalPos = thickness.parent.GlobalToLocal(pieceGlobalPos);
                thickness.SetXY(thicknessLocalPos.x, thicknessLocalPos.y);
            }
        }
        catch (System.Exception)
        {
            // 忽略坐标转换错误
        }
    }
    
    /// <summary>
    /// 开始拖拽
    /// </summary>
    /// <param name="piece">拼块</param>
    public void StartDragging(JigsawPiece piece)
    {
        if (!pieceLayerInfo.ContainsKey(piece)) return;
        
        var info = pieceLayerInfo[piece];
        info.isDragging = true;
        pieceLayerInfo[piece] = info;
        
        // 强制更新到拖拽层级
        ForceDraggingLayerUpdate(piece);
        
        // 如果是组拖拽，同时处理组内其他拼块
        if (piece.GetGroup() != null)
        {
            foreach (var groupPiece in piece.GetGroup().Pieces)
            {
                if (groupPiece != piece && pieceLayerInfo.ContainsKey(groupPiece))
                {
                    var groupInfo = pieceLayerInfo[groupPiece];
                    groupInfo.isDragging = true;
                    pieceLayerInfo[groupPiece] = groupInfo;
                    // 强制更新到拖拽层级
                    ForceDraggingLayerUpdate(groupPiece);
                }
            }
        }
    }
    
    /// <summary>
    /// 强制更新拼块到拖拽层级
    /// </summary>
    /// <param name="piece">拼块</param>
    private void ForceDraggingLayerUpdate(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || !pieceLayerInfo.ContainsKey(piece)) return;

        var info = pieceLayerInfo[piece];
        var thickness = GetThicknessFor(piece);

        try
        {
            // 获取拖拽层级的容器
            var draggingPieceContainer = containerManager.GetPieceContainer(true, info.isOverlay);
            var draggingThicknessContainer = containerManager.GetThicknessContainer(true, info.isOverlay);

            // 移动thickness到拖拽容器
            if (thickness != null && thickness.parent != draggingThicknessContainer && !thickness.isDisposed)
            {
                containerManager.MoveToContainer(thickness, draggingThicknessContainer);
                UpdateThicknessPosition(piece, thickness);
            }

            // 移动piece到拖拽容器
            if (piece.parent != draggingPieceContainer && !piece.isDisposed)
            {
                containerManager.MoveToContainer(piece, draggingPieceContainer);
            }
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"Failed to force dragging layer update: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 停止拖拽
    /// </summary>
    /// <param name="piece">拼块</param>
    public void StopDragging(JigsawPiece piece)
    {
        StopDragging(piece, true);
    }
    
    /// <summary>
    /// 停止拖拽（内部方法，支持防止递归）
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="processGroup">是否处理组内其他拼块</param>
    private void StopDragging(JigsawPiece piece, bool processGroup)
    {
        if (!pieceLayerInfo.ContainsKey(piece)) return;
        
        var info = pieceLayerInfo[piece];
        info.isDragging = false;
        
        // 重新计算网格位置和叠加状态
        Vector2Int newGridPos = CalculateGridPosition(piece);
        Vector2Int oldGridPos = info.gridPosition;
        
        // 如果位置发生变化，更新网格管理
        if (newGridPos != oldGridPos)
        {
            gridManager.RemovePieceFromGrid(oldGridPos, piece);
            gridManager.AddPieceToGrid(newGridPos, piece);
            
            info.gridPosition = newGridPos;
            info.stackIndex = gridManager.GetStackIndex(newGridPos, piece);
            info.isOverlay = gridManager.IsOverlayPosition(newGridPos, piece);
        }
        
        pieceLayerInfo[piece] = info;
        UpdatePieceLayer(piece);
        
        // 如果是组拖拽且需要处理组，同时处理组内其他拼块（防止递归）
        if (processGroup && piece.GetGroup() != null)
        {
            foreach (var groupPiece in piece.GetGroup().Pieces)
            {
                if (groupPiece != piece && pieceLayerInfo.ContainsKey(groupPiece))
                {
                    StopDragging(groupPiece, false); // 不再处理组，防止递归
                }
            }
        }
    }
    
    /// <summary>
    /// 更新拼块位置
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UpdatePiecePosition(JigsawPiece piece)
    {
        if (!pieceLayerInfo.ContainsKey(piece)) return;
        
        var info = pieceLayerInfo[piece];
        Vector2Int newGridPos = CalculateGridPosition(piece);
        
        // 如果网格位置发生变化，更新相关信息
        if (newGridPos != info.gridPosition)
        {
            gridManager.RemovePieceFromGrid(info.gridPosition, piece);
            gridManager.AddPieceToGrid(newGridPos, piece);
            
            // 保留原有的拖拽状态，只更新位置相关信息
            bool originalDragging = info.isDragging;
            
            info.gridPosition = newGridPos;
            info.stackIndex = gridManager.GetStackIndex(newGridPos, piece);
            info.isOverlay = gridManager.IsOverlayPosition(newGridPos, piece);
            info.isDragging = originalDragging; // 保持原有拖拽状态
            
            pieceLayerInfo[piece] = info;
            UpdatePieceLayer(piece);
        }
    }
    
    /// <summary>
    /// 计算拼块的网格位置
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>网格位置</returns>
    private Vector2Int CalculateGridPosition(JigsawPiece piece)
    {
        if (parentPanel == null || piece == null) return Vector2Int.zero;
        
        // 安全检查：确保拼块有有效的父容器
        if (piece.parent == null || piece.isDisposed) return Vector2Int.zero;
        
        try
        {
            Vector2 centerOffset = new Vector2(piece.width * 0.5f, piece.height * 0.5f);
            Vector2 globalCenterPos = piece.LocalToGlobal(centerOffset);
            Vector2 operationLayerLocalPos = parentPanel.GlobalToOperationLayerLocal(globalCenterPos);
            
            return parentPanel.GetGridPosition(operationLayerLocalPos);
        }
        catch (System.Exception)
        {
            // 如果转换失败，返回默认值
            return Vector2Int.zero;
        }
    }
    
    /// <summary>
    /// 获取拼块对应的thickness
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>thickness组件</returns>
    public GComponent GetThicknessFor(JigsawPiece piece)
    {
        if (piece == null) return null;
        
        if (pieceThicknessMap.TryGetValue(piece, out GComponent thickness))
        {
            return thickness;
        }
        
        return null;
    }
    
    /// <summary>
    /// 仅更新thickness位置，不触碰层级逻辑
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="thickness">thickness组件</param>
    public void UpdateThicknessPositionOnly(JigsawPiece piece, GComponent thickness)
    {
        if (piece == null || thickness == null || piece.isDisposed) return;
        
        try
        {
            // 获取拼块的全局位置
            Vector2 pieceGlobalPos = piece.LocalToGlobal(Vector2.zero);
            
            // 转换为thickness父容器的本地坐标
            if (thickness.parent != null)
            {
                Vector2 thicknessLocalPos = thickness.parent.GlobalToLocal(pieceGlobalPos);
                thickness.SetXY(thicknessLocalPos.x, thicknessLocalPos.y);
            }
        }
        catch (System.Exception)
        {
            // 忽略坐标转换错误
        }
    }
    
    /// <summary>
    /// 确保拖拽状态下的piece和thickness在正确的容器中
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="thickness">thickness组件</param>
    public void EnsureDraggingContainerPlacement(JigsawPiece piece, GComponent thickness)
    {
        if (piece == null || piece.isDisposed || !pieceLayerInfo.ContainsKey(piece)) return;
        if (thickness == null || thickness.isDisposed) return;

        try
        {
            var info = pieceLayerInfo[piece];
            if (!info.isDragging) return;

            // 获取拖拽容器
            var draggingPieceContainer = containerManager.GetPieceContainer(true, info.isOverlay);
            var draggingThicknessContainer = containerManager.GetThicknessContainer(true, info.isOverlay);

            // 确保thickness在正确的容器中
            if (thickness.parent != draggingThicknessContainer)
            {
                containerManager.MoveToContainer(thickness, draggingThicknessContainer);
            }

            // 确保piece在正确的容器中
            if (piece.parent != draggingPieceContainer)
            {
                containerManager.MoveToContainer(piece, draggingPieceContainer);
            }
        }
        catch (System.Exception)
        {
            // 忽略设置错误
        }
    }
    
    /// <summary>
    /// 获取拼块的层级信息
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>层级信息</returns>
    public LayerInfo GetLayerInfo(JigsawPiece piece)
    {
        if (pieceLayerInfo.ContainsKey(piece))
            return pieceLayerInfo[piece];
        return new LayerInfo(Vector2Int.zero, 0, false, false);
    }
    
    /// <summary>
    /// 压缩层级索引，避免索引过大
    /// </summary>
    public void OptimizeLayerIndices()
    {
        var allPieces = pieceLayerInfo.Keys.ToList();
        foreach (var piece in allPieces)
        {
            UpdatePieceLayer(piece);
        }
    }
    
    /// <summary>
    /// 获取网格位置管理器
    /// </summary>
    /// <returns>网格位置管理器</returns>
    public GridPositionManager GetGridManager()
    {
        return gridManager;
    }
    
    /// <summary>
    /// 获取所有已注册的拼块
    /// </summary>
    /// <returns>拼块列表</returns>
    public List<JigsawPiece> GetAllRegisteredPieces()
    {
        return pieceLayerInfo.Keys.Where(piece => piece != null && !piece.isDisposed).ToList();
    }
    
    /// <summary>
    /// 清空所有层级信息
    /// </summary>
    public void Clear()
    {
        // 清理所有thickness
        foreach (var piece in pieceThicknessMap.Keys.ToList())
        {
            RemovePieceThickness(piece);
        }

        pieceLayerInfo.Clear();
        gridManager.Clear();
        pieceThicknessMap.Clear();

        // 清理容器管理器
        containerManager?.Clear();
    }

    /// <summary>
    /// 销毁层级管理器
    /// </summary>
    public void Dispose()
    {
        Clear();
        containerManager?.Dispose();
        containerManager = null;
    }
}