using UnityEngine;
using FairyGUI;

/// <summary>
/// 层级容器管理系统测试类
/// </summary>
public class LayerContainerTest : MonoBehaviour
{
    [Header("测试设置")]
    public bool enableDebugLogs = true;
    
    private LayerContainerManager containerManager;
    private GComponent testRoot;
    
    void Start()
    {
        if (enableDebugLogs)
        {
            TestLayerContainerSystem();
        }
    }
    
    /// <summary>
    /// 测试层级容器系统
    /// </summary>
    private void TestLayerContainerSystem()
    {
        Debug.Log("=== 开始测试层级容器管理系统 ===");
        
        // 创建测试根容器
        testRoot = new GComponent();
        testRoot.name = "TestRoot";
        testRoot.SetSize(1920, 1080);
        
        // 创建容器管理器
        containerManager = new LayerContainerManager(testRoot);
        
        // 测试容器创建
        TestContainerCreation();
        
        // 测试容器获取
        TestContainerRetrieval();
        
        // 测试对象移动
        TestObjectMovement();
        
        Debug.Log("=== 层级容器管理系统测试完成 ===");
    }
    
    /// <summary>
    /// 测试容器创建
    /// </summary>
    private void TestContainerCreation()
    {
        Debug.Log("--- 测试容器创建 ---");
        
        // 检查所有预期的容器是否都被创建
        string[] expectedContainers = {
            LayerContainerManager.GRID_CONTAINER,
            LayerContainerManager.THICKNESS_CONTAINER,
            LayerContainerManager.PIECE_CONTAINER,
            LayerContainerManager.OVERLAY_THICKNESS_CONTAINER,
            LayerContainerManager.OVERLAY_PIECE_CONTAINER,
            LayerContainerManager.DRAGGING_THICKNESS_CONTAINER,
            LayerContainerManager.DRAGGING_PIECE_CONTAINER
        };
        
        foreach (string containerName in expectedContainers)
        {
            var container = containerManager.GetContainer(containerName);
            if (container != null)
            {
                Debug.Log($"✓ 容器 {containerName} 创建成功，sortingOrder: {container.sortingOrder}");
            }
            else
            {
                Debug.LogError($"✗ 容器 {containerName} 创建失败");
            }
        }
    }
    
    /// <summary>
    /// 测试容器获取
    /// </summary>
    private void TestContainerRetrieval()
    {
        Debug.Log("--- 测试容器获取 ---");
        
        // 测试piece容器获取
        var normalPieceContainer = containerManager.GetPieceContainer(false, false);
        var overlayPieceContainer = containerManager.GetPieceContainer(false, true);
        var draggingPieceContainer = containerManager.GetPieceContainer(true, false);
        
        Debug.Log($"✓ 普通piece容器: {normalPieceContainer?.name}");
        Debug.Log($"✓ 叠加piece容器: {overlayPieceContainer?.name}");
        Debug.Log($"✓ 拖拽piece容器: {draggingPieceContainer?.name}");
        
        // 测试thickness容器获取
        var normalThicknessContainer = containerManager.GetThicknessContainer(false, false);
        var overlayThicknessContainer = containerManager.GetThicknessContainer(false, true);
        var draggingThicknessContainer = containerManager.GetThicknessContainer(true, false);
        
        Debug.Log($"✓ 普通thickness容器: {normalThicknessContainer?.name}");
        Debug.Log($"✓ 叠加thickness容器: {overlayThicknessContainer?.name}");
        Debug.Log($"✓ 拖拽thickness容器: {draggingThicknessContainer?.name}");
    }
    
    /// <summary>
    /// 测试对象移动
    /// </summary>
    private void TestObjectMovement()
    {
        Debug.Log("--- 测试对象移动 ---");
        
        // 创建测试对象
        var testObject = new GComponent();
        testObject.name = "TestObject";
        testObject.SetSize(100, 100);
        
        // 获取不同的容器
        var pieceContainer = containerManager.GetPieceContainer(false, false);
        var draggingContainer = containerManager.GetPieceContainer(true, false);
        
        // 测试移动到piece容器
        containerManager.MoveToContainer(testObject, pieceContainer);
        Debug.Log($"✓ 对象移动到piece容器: {testObject.parent?.name}");
        
        // 测试移动到拖拽容器
        containerManager.MoveToContainer(testObject, draggingContainer);
        Debug.Log($"✓ 对象移动到拖拽容器: {testObject.parent?.name}");
        
        // 清理测试对象
        testObject.Dispose();
    }
    
    void OnDestroy()
    {
        // 清理资源
        containerManager?.Dispose();
        testRoot?.Dispose();
    }
}
